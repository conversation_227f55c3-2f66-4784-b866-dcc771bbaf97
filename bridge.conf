# SITP-TCP Bridge Configuration File

[tcp]
# TCP server listen address and port
listen_ip = 127.0.0.1
listen_port = 8080

[sitp]
# SITP configuration
interface = eth0
mtu = 1500
protocol = 0x8888
local_board_id = 0xFF10
remote_board_id = 0xFF18
local_port = 0
remote_port = 0
send_delay_ns = 400

[log]
# Logging configuration
# Levels: TRACE, DEBUG, INFO, WARN, ERROR, FATAL
level = INFO
to_file = 0
filename = bridge.log

[general]
# Error tolerance padding size (bytes)
padding_size = 16
# Enable verbose mode (hex dump data) - 0=disabled, 1=enabled
verbose = 0
