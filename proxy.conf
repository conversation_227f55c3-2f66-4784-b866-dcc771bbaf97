# SITP-TCP Proxy Configuration File

[sitp]
# SITP configuration
interface = eth0
mtu = 1500
protocol = 0x8888
local_board_id = 0xFF18
remote_board_id = 0xFF10
local_port = 0
remote_port = 0
send_delay_ns = 400

[target]
# Target server configuration
host = 127.0.0.1
port = 80

[connection]
# Connection management
timeout = 30
pool_size = 100

[log]
# Logging configuration
# Levels: TRACE, DEBUG, INFO, WARN, ERROR, FATAL
level = INFO
to_file = 0
filename = proxy.log

[general]
# Error tolerance padding size (bytes)
padding_size = 16
# Enable verbose mode (hex dump data) - 0=disabled, 1=enabled
verbose = 0
