#include "proxy/pipes.h"
#include "log.h"
#include <unistd.h>
#include <fcntl.h>
#include <errno.h>
#include <string.h>

// External event callbacks (declared in data_flow.c)
extern void sitp_to_tcp_pipe_callback(int fd, short event, void *arg);
extern void tcp_to_sitp_pipe_callback(int fd, short event, void *arg);

int proxy_pipes_init(proxy_pipes_t *pipes, struct event_base *base) {
    // Create SITP to TCP pipe
    if (pipe(pipes->sitp_to_tcp_pipe) == -1) {
        log_error("Failed to create sitp_to_tcp pipe: %s", strerror(errno));
        return -1;
    }

    // Create TCP to SITP pipe
    if (pipe(pipes->tcp_to_sitp_pipe) == -1) {
        log_error("Failed to create tcp_to_sitp pipe: %s", strerror(errno));
        close(pipes->sitp_to_tcp_pipe[0]);
        close(pipes->sitp_to_tcp_pipe[1]);
        return -1;
    }

    // Set non-blocking mode
    fcntl(pipes->sitp_to_tcp_pipe[0], F_SETFL, O_NONBLOCK);
    fcntl(pipes->sitp_to_tcp_pipe[1], F_SETFL, O_NONBLOCK);
    fcntl(pipes->tcp_to_sitp_pipe[0], F_SETFL, O_NONBLOCK);
    fcntl(pipes->tcp_to_sitp_pipe[1], F_SETFL, O_NONBLOCK);

    // Create events for pipe monitoring
    pipes->sitp_to_tcp_event = event_new(base, pipes->sitp_to_tcp_pipe[0],
                                         EV_READ | EV_PERSIST,
                                         sitp_to_tcp_pipe_callback, pipes);
    if (!pipes->sitp_to_tcp_event) {
        log_error("Failed to create sitp_to_tcp event");
        goto cleanup_pipes;
    }

    pipes->tcp_to_sitp_event = event_new(base, pipes->tcp_to_sitp_pipe[0],
                                        EV_READ | EV_PERSIST,
                                        tcp_to_sitp_pipe_callback, pipes);
    if (!pipes->tcp_to_sitp_event) {
        log_error("Failed to create tcp_to_sitp event");
        goto cleanup_events;
    }

    // Add events to event loop
    if (event_add(pipes->sitp_to_tcp_event, NULL) < 0) {
        log_error("Failed to add sitp_to_tcp event");
        goto cleanup_all;
    }

    if (event_add(pipes->tcp_to_sitp_event, NULL) < 0) {
        log_error("Failed to add tcp_to_sitp event");
        goto cleanup_all;
    }

    log_info("Proxy pipes initialized successfully");
    return 0;

cleanup_all:
    event_free(pipes->tcp_to_sitp_event);
cleanup_events:
    event_free(pipes->sitp_to_tcp_event);
cleanup_pipes:
    close(pipes->sitp_to_tcp_pipe[0]);
    close(pipes->sitp_to_tcp_pipe[1]);
    close(pipes->tcp_to_sitp_pipe[0]);
    close(pipes->tcp_to_sitp_pipe[1]);
    return -1;
}

void proxy_pipes_cleanup(proxy_pipes_t *pipes) {
    if (pipes->sitp_to_tcp_event) {
        event_free(pipes->sitp_to_tcp_event);
        pipes->sitp_to_tcp_event = NULL;
    }

    if (pipes->tcp_to_sitp_event) {
        event_free(pipes->tcp_to_sitp_event);
        pipes->tcp_to_sitp_event = NULL;
    }

    if (pipes->sitp_to_tcp_pipe[0] >= 0) {
        close(pipes->sitp_to_tcp_pipe[0]);
        close(pipes->sitp_to_tcp_pipe[1]);
        pipes->sitp_to_tcp_pipe[0] = pipes->sitp_to_tcp_pipe[1] = -1;
    }

    if (pipes->tcp_to_sitp_pipe[0] >= 0) {
        close(pipes->tcp_to_sitp_pipe[0]);
        close(pipes->tcp_to_sitp_pipe[1]);
        pipes->tcp_to_sitp_pipe[0] = pipes->tcp_to_sitp_pipe[1] = -1;
    }
}

int proxy_pipes_write_sitp_to_tcp(proxy_pipes_t *pipes, const uint8_t *data, size_t len) {
    ssize_t written = write(pipes->sitp_to_tcp_pipe[1], data, len);
    if (written < 0 && errno != EAGAIN && errno != EWOULDBLOCK) {
        log_error("Failed to write to sitp_to_tcp pipe: %s", strerror(errno));
        return -1;
    }
    return written;
}

int proxy_pipes_write_tcp_to_sitp(proxy_pipes_t *pipes, const uint8_t *data, size_t len) {
    ssize_t written = write(pipes->tcp_to_sitp_pipe[1], data, len);
    if (written < 0 && errno != EAGAIN && errno != EWOULDBLOCK) {
        log_error("Failed to write to tcp_to_sitp pipe: %s", strerror(errno));
        return -1;
    }
    return written;
}


