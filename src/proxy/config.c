#define _GNU_SOURCE
#include "proxy/config.h"
#include "ini.h"
#include "log.h"
#include <stdlib.h>
#include <string.h>

static int config_handler(void *user, const char *section, const char *name, const char *value) {
    proxy_config_t *config = (proxy_config_t*)user;
    
    if (strcmp(section, "sitp") == 0) {
        if (strcmp(name, "interface") == 0) {
            config->sitp_interface = strdup(value);
        } else if (strcmp(name, "mtu") == 0) {
            config->sitp_mtu = atoi(value);
        } else if (strcmp(name, "protocol") == 0) {
            config->sitp_protocol = (uint16_t)strtoul(value, NULL, 0);
        } else if (strcmp(name, "local_board_id") == 0) {
            config->sitp_local_board_id = (uint16_t)strtoul(value, NULL, 0);
        } else if (strcmp(name, "remote_board_id") == 0) {
            config->sitp_remote_board_id = (uint16_t)strtoul(value, NULL, 0);
        } else if (strcmp(name, "local_port") == 0) {
            config->sitp_local_port = (uint16_t)atoi(value);
        } else if (strcmp(name, "remote_port") == 0) {
            config->sitp_remote_port = (uint16_t)atoi(value);
        } else if (strcmp(name, "send_delay_ns") == 0) {
            config->sitp_send_delay_ns = atol(value);
        }
    } else if (strcmp(section, "target") == 0) {
        if (strcmp(name, "host") == 0) {
            config->target_host = strdup(value);
        } else if (strcmp(name, "port") == 0) {
            config->target_port = atoi(value);
        }
    } else if (strcmp(section, "connection") == 0) {
        if (strcmp(name, "timeout") == 0) {
            config->connection_timeout = atoi(value);
        } else if (strcmp(name, "pool_size") == 0) {
            config->connection_pool_size = atoi(value);
        }
    } else if (strcmp(section, "log") == 0) {
        if (strcmp(name, "level") == 0) {
            if (strcmp(value, "TRACE") == 0) config->log_level = LOG_TRACE;
            else if (strcmp(value, "DEBUG") == 0) config->log_level = LOG_DEBUG;
            else if (strcmp(value, "INFO") == 0) config->log_level = LOG_INFO;
            else if (strcmp(value, "WARN") == 0) config->log_level = LOG_WARN;
            else if (strcmp(value, "ERROR") == 0) config->log_level = LOG_ERROR;
            else if (strcmp(value, "FATAL") == 0) config->log_level = LOG_FATAL;
            else config->log_level = LOG_INFO;
        } else if (strcmp(name, "to_file") == 0) {
            config->log_to_file = atoi(value);
        } else if (strcmp(name, "filename") == 0) {
            config->log_filename = strdup(value);
        }
    } else if (strcmp(section, "general") == 0) {
        if (strcmp(name, "padding_size") == 0) {
            config->padding_size = atoi(value);
        } else if (strcmp(name, "verbose") == 0) {
            config->verbose = atoi(value);
        }
    }
    
    return 1;
}

int proxy_config_load(const char *config_file, proxy_config_t *config) {
    memset(config, 0, sizeof(proxy_config_t));
    
    // Set default values
    config->sitp_interface = strdup("eth0");
    config->sitp_mtu = 1500;
    config->sitp_protocol = 0x8889;
    config->sitp_local_board_id = 0xFF18;
    config->sitp_remote_board_id = 0xFF10;
    config->sitp_local_port = 0;
    config->sitp_remote_port = 0;
    config->sitp_send_delay_ns = 400;
    config->target_host = strdup("127.0.0.1");
    config->target_port = 80;
    config->connection_timeout = 30;
    config->connection_pool_size = 100;
    config->log_level = LOG_INFO;
    config->log_to_file = 0;
    config->log_filename = strdup("proxy.log");
    config->padding_size = 16;
    config->verbose = 0;
    
    if (ini_parse(config_file, config_handler, config) < 0) {
        log_error("Failed to load config file: %s", config_file);
        return -1;
    }
    
    log_info("Configuration loaded from %s", config_file);
    return 0;
}

void proxy_config_free(proxy_config_t *config) {
    if (config->sitp_interface) free(config->sitp_interface);
    if (config->target_host) free(config->target_host);
    if (config->log_filename) free(config->log_filename);
    memset(config, 0, sizeof(proxy_config_t));
}