#include "proxy/logger.h"
#include "log.h"
#include <stdio.h>
#include <stdlib.h>

static FILE *log_file = NULL;

int proxy_logger_init(proxy_config_t *config) {
    log_set_level(config->log_level);
    
    if (config->log_to_file && config->log_filename) {
        log_file = fopen(config->log_filename, "a");
        if (log_file) {
            log_add_fp(log_file, config->log_level);
            log_info("Logging to file: %s", config->log_filename);
        } else {
            log_warn("Failed to open log file: %s", config->log_filename);
            return -1;
        }
    }
    
    return 0;
}

void proxy_logger_cleanup(void) {
    if (log_file) {
        fclose(log_file);
        log_file = NULL;
    }
}