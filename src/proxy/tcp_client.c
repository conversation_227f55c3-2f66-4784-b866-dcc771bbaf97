#include "proxy/tcp_client.h"
#include "proxy/data_flow.h"
#include "log.h"
#include <stdlib.h>
#include <string.h>
#include <errno.h>
#include <unistd.h>
#include <sys/socket.h>
#include <netinet/in.h>
#include <arpa/inet.h>
#include <time.h>
#include <event2/buffer.h>

static void tcp_client_read_cb(struct bufferevent *bev, void *ctx);
static void tcp_client_event_cb(struct bufferevent *bev, short events, void *ctx);
static void tcp_connection_cleanup(tcp_connection_t *conn);

int tcp_client_manager_init(tcp_client_manager_t *manager, struct event_base *base, 
                           proxy_config_t *config, proxy_pipes_t *pipes) {
    memset(manager, 0, sizeof(tcp_client_manager_t));
    
    manager->config = config;
    manager->pipes = pipes;
    manager->base = base;
    manager->connections = NULL;
    manager->next_connection_id = 1;
    
    if (pthread_mutex_init(&manager->connections_mutex, NULL) != 0) {
        log_error("Failed to initialize connections mutex");
        return -1;
    }
    
    log_info("TCP client manager initialized");
    return 0;
}

void tcp_client_manager_cleanup(tcp_client_manager_t *manager) {
    pthread_mutex_lock(&manager->connections_mutex);
    
    tcp_connection_t *conn = manager->connections;
    while (conn) {
        tcp_connection_t *next = conn->next;
        tcp_connection_cleanup(conn);
        free(conn);
        conn = next;
    }
    manager->connections = NULL;
    
    pthread_mutex_unlock(&manager->connections_mutex);
    pthread_mutex_destroy(&manager->connections_mutex);
    
    log_info("TCP client manager cleaned up");
}

tcp_connection_t *tcp_client_get_connection(tcp_client_manager_t *manager, uint32_t connection_id) {
    pthread_mutex_lock(&manager->connections_mutex);
    
    tcp_connection_t *conn = manager->connections;
    while (conn) {
        if (conn->connection_id == connection_id) {
            pthread_mutex_unlock(&manager->connections_mutex);
            return conn;
        }
        conn = conn->next;
    }
    
    pthread_mutex_unlock(&manager->connections_mutex);
    return NULL;
}

tcp_connection_t *tcp_client_create_connection(tcp_client_manager_t *manager, uint32_t connection_id) {
    // Check if connection already exists
    tcp_connection_t *existing = tcp_client_get_connection(manager, connection_id);
    if (existing) {
        return existing;
    }
    
    // Create new connection
    tcp_connection_t *conn = calloc(1, sizeof(tcp_connection_t));
    if (!conn) {
        log_error("Failed to allocate memory for TCP connection");
        return NULL;
    }
    
    conn->connection_id = connection_id;
    conn->base = manager->base;
    conn->connected = 0;
    conn->last_activity = time(NULL);
    
    // Create bufferevent for the connection
    conn->bev = bufferevent_socket_new(manager->base, -1, BEV_OPT_CLOSE_ON_FREE);
    if (!conn->bev) {
        log_error("Failed to create bufferevent for connection %u", connection_id);
        free(conn);
        return NULL;
    }
    
    // Set callbacks
    bufferevent_setcb(conn->bev, tcp_client_read_cb, NULL, tcp_client_event_cb, conn);
    bufferevent_enable(conn->bev, EV_READ | EV_WRITE);
    
    // Connect to target server
    struct sockaddr_in sin;
    memset(&sin, 0, sizeof(sin));
    sin.sin_family = AF_INET;
    sin.sin_port = htons(manager->config->target_port);
    if (inet_pton(AF_INET, manager->config->target_host, &sin.sin_addr) <= 0) {
        log_error("Invalid target host: %s", manager->config->target_host);
        bufferevent_free(conn->bev);
        free(conn);
        return NULL;
    }
    
    if (bufferevent_socket_connect(conn->bev, (struct sockaddr*)&sin, sizeof(sin)) < 0) {
        log_error("Failed to initiate connection to %s:%d for connection %u",
                 manager->config->target_host, manager->config->target_port, connection_id);
        bufferevent_free(conn->bev);
        free(conn);
        return NULL;
    }
    
    // Add to connection list
    pthread_mutex_lock(&manager->connections_mutex);
    conn->next = manager->connections;
    manager->connections = conn;
    pthread_mutex_unlock(&manager->connections_mutex);
    
    log_info("Created TCP connection %u to %s:%d", connection_id,
             manager->config->target_host, manager->config->target_port);
    
    return conn;
}

void tcp_client_close_connection(tcp_client_manager_t *manager, uint32_t connection_id) {
    pthread_mutex_lock(&manager->connections_mutex);
    
    tcp_connection_t **conn_ptr = &manager->connections;
    while (*conn_ptr) {
        if ((*conn_ptr)->connection_id == connection_id) {
            tcp_connection_t *conn = *conn_ptr;
            *conn_ptr = conn->next;
            
            log_info("Closing TCP connection %u", connection_id);
            tcp_connection_cleanup(conn);
            free(conn);
            break;
        }
        conn_ptr = &(*conn_ptr)->next;
    }
    
    pthread_mutex_unlock(&manager->connections_mutex);
}

int tcp_client_send_data(tcp_connection_t *conn, const uint8_t *data, size_t len) {
    if (!conn || !conn->bev || !conn->connected) {
        return -1;
    }
    
    conn->last_activity = time(NULL);
    
    if (bufferevent_write(conn->bev, data, len) < 0) {
        log_error("Failed to write data to TCP connection %u", conn->connection_id);
        return -1;
    }
    
    return len;
}

static void tcp_client_read_cb(struct bufferevent *bev, void *ctx) {
    tcp_connection_t *conn = (tcp_connection_t*)ctx;
    struct evbuffer *input = bufferevent_get_input(bev);
    
    size_t len = evbuffer_get_length(input);
    if (len == 0) {
        return;
    }
    
    uint8_t *data = malloc(len);
    if (!data) {
        log_error("Failed to allocate memory for TCP read data");
        return;
    }
    
    if (evbuffer_remove(input, data, len) < 0) {
        log_error("Failed to read data from TCP connection %u", conn->connection_id);
        free(data);
        return;
    }
    
    conn->last_activity = time(NULL);
    
    // Forward data to proxy data flow
    proxy_tcp_data_received(conn->connection_id, data, len);
    
    log_debug("Received %zu bytes from TCP connection %u", len, conn->connection_id);
    free(data);
}

static void tcp_client_event_cb(struct bufferevent *bev, short events, void *ctx) {
    tcp_connection_t *conn = (tcp_connection_t*)ctx;
    
    if (events & BEV_EVENT_CONNECTED) {
        conn->connected = 1;
        conn->last_activity = time(NULL);
        log_info("TCP connection %u established", conn->connection_id);
    }
    
    if (events & (BEV_EVENT_ERROR | BEV_EVENT_EOF)) {
        if (events & BEV_EVENT_ERROR) {
            log_error("TCP connection %u error: %s", conn->connection_id, 
                     evutil_socket_error_to_string(EVUTIL_SOCKET_ERROR()));
        } else {
            log_info("TCP connection %u closed by remote", conn->connection_id);
        }
        
        conn->connected = 0;
        
        // Notify proxy data flow about connection closure
        proxy_tcp_connection_closed(conn->connection_id);
    }
}

static void tcp_connection_cleanup(tcp_connection_t *conn) {
    if (conn->bev) {
        bufferevent_free(conn->bev);
        conn->bev = NULL;
    }
    conn->connected = 0;
}