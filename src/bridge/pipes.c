#include "bridge/pipes.h"
#include "log.h"
#include <unistd.h>
#include <fcntl.h>
#include <stdlib.h>

static int set_nonblocking(int fd) {
    int flags = fcntl(fd, F_GETFL);
    if (flags < 0) {
        log_error("fcntl F_GETFL failed");
        return -1;
    }
    if (fcntl(fd, F_SETFL, flags | O_NONBLOCK) < 0) {
        log_error("fcntl F_SETFL failed");
        return -1;
    }
    return 0;
}

int bridge_pipes_init(bridge_pipes_t *pipes, struct event_base *base) {
    pipes->base = base;
    pipes->tcp_to_sitp_event = NULL;
    pipes->sitp_to_tcp_event = NULL;
    
    if (pipe(pipes->tcp_to_sitp_fd) < 0) {
        log_error("Failed to create tcp_to_sitp pipe");
        return -1;
    }
    
    if (pipe(pipes->sitp_to_tcp_fd) < 0) {
        log_error("Failed to create sitp_to_tcp pipe");
        close(pipes->tcp_to_sitp_fd[0]);
        close(pipes->tcp_to_sitp_fd[1]);
        return -1;
    }
    
    if (set_nonblocking(pipes->tcp_to_sitp_fd[0]) < 0 ||
        set_nonblocking(pipes->tcp_to_sitp_fd[1]) < 0 ||
        set_nonblocking(pipes->sitp_to_tcp_fd[0]) < 0 ||
        set_nonblocking(pipes->sitp_to_tcp_fd[1]) < 0) {
        bridge_pipes_cleanup(pipes);
        return -1;
    }
    
    log_info("Bridge pipes initialized successfully");
    return 0;
}

void bridge_pipes_cleanup(bridge_pipes_t *pipes) {
    if (pipes->tcp_to_sitp_event) {
        event_free(pipes->tcp_to_sitp_event);
        pipes->tcp_to_sitp_event = NULL;
    }
    
    if (pipes->sitp_to_tcp_event) {
        event_free(pipes->sitp_to_tcp_event);
        pipes->sitp_to_tcp_event = NULL;
    }
    
    if (pipes->tcp_to_sitp_fd[0] >= 0) {
        close(pipes->tcp_to_sitp_fd[0]);
        pipes->tcp_to_sitp_fd[0] = -1;
    }
    if (pipes->tcp_to_sitp_fd[1] >= 0) {
        close(pipes->tcp_to_sitp_fd[1]);
        pipes->tcp_to_sitp_fd[1] = -1;
    }
    if (pipes->sitp_to_tcp_fd[0] >= 0) {
        close(pipes->sitp_to_tcp_fd[0]);
        pipes->sitp_to_tcp_fd[0] = -1;
    }
    if (pipes->sitp_to_tcp_fd[1] >= 0) {
        close(pipes->sitp_to_tcp_fd[1]);
        pipes->sitp_to_tcp_fd[1] = -1;
    }
    
    log_info("Bridge pipes cleaned up");
}

int bridge_pipes_set_callbacks(bridge_pipes_t *pipes, 
                               pipe_data_callback_t tcp_to_sitp_cb,
                               pipe_data_callback_t sitp_to_tcp_cb,
                               void *tcp_to_sitp_arg,
                               void *sitp_to_tcp_arg) {
    pipes->tcp_to_sitp_event = event_new(pipes->base, pipes->tcp_to_sitp_fd[0], 
                                         EV_READ | EV_PERSIST, tcp_to_sitp_cb, tcp_to_sitp_arg);
    if (!pipes->tcp_to_sitp_event) {
        log_error("Failed to create tcp_to_sitp event");
        return -1;
    }
    
    pipes->sitp_to_tcp_event = event_new(pipes->base, pipes->sitp_to_tcp_fd[0], 
                                         EV_READ | EV_PERSIST, sitp_to_tcp_cb, sitp_to_tcp_arg);
    if (!pipes->sitp_to_tcp_event) {
        log_error("Failed to create sitp_to_tcp event");
        event_free(pipes->tcp_to_sitp_event);
        pipes->tcp_to_sitp_event = NULL;
        return -1;
    }
    
    if (event_add(pipes->tcp_to_sitp_event, NULL) < 0) {
        log_error("Failed to add tcp_to_sitp event");
        return -1;
    }
    
    if (event_add(pipes->sitp_to_tcp_event, NULL) < 0) {
        log_error("Failed to add sitp_to_tcp event");
        return -1;
    }
    
    log_info("Pipe callbacks set successfully");
    return 0;
}