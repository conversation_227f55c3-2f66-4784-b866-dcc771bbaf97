#define _GNU_SOURCE
#include "bridge/config.h"
#include "ini.h"
#include "log.h"
#include <stdlib.h>
#include <string.h>

static int config_handler(void *user, const char *section, const char *name, const char *value) {
    bridge_config_t *config = (bridge_config_t*)user;
    
    if (strcmp(section, "tcp") == 0) {
        if (strcmp(name, "listen_ip") == 0) {
            config->listen_ip = strdup(value);
        } else if (strcmp(name, "listen_port") == 0) {
            config->listen_port = atoi(value);
        }
    } else if (strcmp(section, "sitp") == 0) {
        if (strcmp(name, "interface") == 0) {
            config->sitp_interface = strdup(value);
        } else if (strcmp(name, "mtu") == 0) {
            config->sitp_mtu = atoi(value);
        } else if (strcmp(name, "protocol") == 0) {
            config->sitp_protocol = (uint16_t)strtoul(value, NULL, 0);
        } else if (strcmp(name, "local_board_id") == 0) {
            config->sitp_local_board_id = (uint16_t)strtoul(value, NULL, 0);
        } else if (strcmp(name, "remote_board_id") == 0) {
            config->sitp_remote_board_id = (uint16_t)strtoul(value, NULL, 0);
        } else if (strcmp(name, "local_port") == 0) {
            config->sitp_local_port = (uint16_t)atoi(value);
        } else if (strcmp(name, "remote_port") == 0) {
            config->sitp_remote_port = (uint16_t)atoi(value);
        } else if (strcmp(name, "send_delay_ns") == 0) {
            config->sitp_send_delay_ns = atol(value);
        }
    } else if (strcmp(section, "log") == 0) {
        if (strcmp(name, "level") == 0) {
            if (strcmp(value, "TRACE") == 0) config->log_level = LOG_TRACE;
            else if (strcmp(value, "DEBUG") == 0) config->log_level = LOG_DEBUG;
            else if (strcmp(value, "INFO") == 0) config->log_level = LOG_INFO;
            else if (strcmp(value, "WARN") == 0) config->log_level = LOG_WARN;
            else if (strcmp(value, "ERROR") == 0) config->log_level = LOG_ERROR;
            else if (strcmp(value, "FATAL") == 0) config->log_level = LOG_FATAL;
            else config->log_level = LOG_INFO;
        } else if (strcmp(name, "to_file") == 0) {
            config->log_to_file = atoi(value);
        } else if (strcmp(name, "filename") == 0) {
            config->log_filename = strdup(value);
        }
    } else if (strcmp(section, "general") == 0) {
        if (strcmp(name, "padding_size") == 0) {
            config->padding_size = atoi(value);
        } else if (strcmp(name, "verbose") == 0) {
            config->verbose = atoi(value);
        }
    }
    
    return 1;
}

int bridge_config_load(const char *config_file, bridge_config_t *config) {
    memset(config, 0, sizeof(bridge_config_t));
    
    config->listen_ip = strdup("127.0.0.1");
    config->listen_port = 8080;
    config->sitp_interface = strdup("eth0");
    config->sitp_mtu = 1500;
    config->sitp_protocol = 0x8888;
    config->sitp_local_board_id = 0xFF10;
    config->sitp_remote_board_id = 0xFF12;
    config->sitp_local_port = 0;
    config->sitp_remote_port = 0;
    config->sitp_send_delay_ns = 0;
    config->log_level = LOG_INFO;
    config->log_to_file = 0;
    config->log_filename = strdup("bridge.log");
    config->padding_size = 16;
    config->verbose = 0;
    
    if (ini_parse(config_file, config_handler, config) < 0) {
        log_error("Failed to load config file: %s", config_file);
        return -1;
    }
    
    log_info("Configuration loaded from %s", config_file);
    return 0;
}

void bridge_config_free(bridge_config_t *config) {
    if (config->listen_ip) free(config->listen_ip);
    if (config->sitp_interface) free(config->sitp_interface);
    if (config->log_filename) free(config->log_filename);
    memset(config, 0, sizeof(bridge_config_t));
}