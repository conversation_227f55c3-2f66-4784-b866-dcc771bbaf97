#include "common/protocol.h"
#include "log.h"
#include <stdlib.h>
#include <string.h>

padded_message_t *create_padded_message(uint32_t client_fd, message_cmd_t cmd, 
                                      const uint8_t *data, uint32_t data_len, 
                                      int padding_size) {
    padded_message_t *pmsg = malloc(sizeof(padded_message_t));
    if (!pmsg) {
        log_error("Failed to allocate memory for padded message");
        return NULL;
    }
    
    size_t msg_size = sizeof(message_t) + data_len;
    pmsg->total_size = padding_size + msg_size + padding_size;
    
    pmsg->buffer = calloc(1, pmsg->total_size);
    if (!pmsg->buffer) {
        log_error("Failed to allocate memory for message buffer");
        free(pmsg);
        return NULL;
    }
    
    pmsg->padding_start = pmsg->buffer;
    pmsg->msg = (message_t *)(pmsg->buffer + padding_size);
    pmsg->padding_end = pmsg->buffer + padding_size + msg_size;
    
    pmsg->msg->client_fd = client_fd;
    pmsg->msg->cmd = cmd;
    pmsg->msg->data_len = data_len;
    
    if (data && data_len > 0) {
        memcpy(pmsg->msg->data, data, data_len);
    }
    
    log_debug("Created padded message: fd=%u, cmd=%d, data_len=%u, total_size=%zu", 
              client_fd, cmd, data_len, pmsg->total_size);
    
    return pmsg;
}

message_t *extract_message_from_padded(const uint8_t *buffer, size_t buffer_len, 
                                     int padding_size) {
    if (!buffer || buffer_len < (size_t)(2 * padding_size + sizeof(message_t))) {
        log_error("Invalid buffer for message extraction");
        return NULL;
    }
    
    message_t *src_msg = (message_t *)(buffer + padding_size);
    size_t expected_size = padding_size + sizeof(message_t) + src_msg->data_len + padding_size;
    
    if (buffer_len < expected_size) {
        log_error("Buffer too small for message: expected=%zu, actual=%zu", 
                  expected_size, buffer_len);
        return NULL;
    }
    
    size_t msg_size = sizeof(message_t) + src_msg->data_len;
    message_t *msg = malloc(msg_size);
    if (!msg) {
        log_error("Failed to allocate memory for extracted message");
        return NULL;
    }
    
    memcpy(msg, src_msg, msg_size);
    
    log_debug("Extracted message: fd=%u, cmd=%d, data_len=%u", 
              msg->client_fd, msg->cmd, msg->data_len);
    
    return msg;
}

void free_padded_message(padded_message_t *pmsg) {
    if (pmsg) {
        if (pmsg->buffer) {
            free(pmsg->buffer);
        }
        free(pmsg);
    }
}

size_t get_padded_message_size(uint32_t data_len, int padding_size) {
    return padding_size + sizeof(message_t) + data_len + padding_size;
}

int validate_padded_message(const uint8_t *buffer, size_t buffer_len, int padding_size) {
    if (!buffer || buffer_len < (size_t)(2 * padding_size + sizeof(message_t))) {
        return 0;
    }
    
    message_t *msg = (message_t *)(buffer + padding_size);
    size_t expected_size = get_padded_message_size(msg->data_len, padding_size);
    
    if (buffer_len < expected_size) {
        return 0;
    }
    
    for (int i = 0; i < padding_size; i++) {
        if (buffer[i] != 0 || buffer[padding_size + sizeof(message_t) + msg->data_len + i] != 0) {
            return 0;
        }
    }
    
    return 1;
}