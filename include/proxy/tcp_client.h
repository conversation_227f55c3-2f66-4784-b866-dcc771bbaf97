#ifndef PROXY_TCP_CLIENT_H
#define PROXY_TCP_CLIENT_H

#include <event2/event.h>
#include <event2/bufferevent.h>
#include <stdint.h>
#include <pthread.h>
#include "proxy/config.h"
#include "proxy/pipes.h"

typedef struct tcp_connection {
    uint32_t connection_id;
    struct bufferevent *bev;
    struct event_base *base;
    int connected;
    time_t last_activity;
    struct tcp_connection *next;
} tcp_connection_t;

typedef struct {
    proxy_config_t *config;
    proxy_pipes_t *pipes;
    struct event_base *base;
    tcp_connection_t *connections;
    uint32_t next_connection_id;
    pthread_mutex_t connections_mutex;
} tcp_client_manager_t;

int tcp_client_manager_init(tcp_client_manager_t *manager, struct event_base *base, 
                           proxy_config_t *config, proxy_pipes_t *pipes);
void tcp_client_manager_cleanup(tcp_client_manager_t *manager);

tcp_connection_t *tcp_client_get_connection(tcp_client_manager_t *manager, uint32_t connection_id);
tcp_connection_t *tcp_client_create_connection(tcp_client_manager_t *manager, uint32_t connection_id);
void tcp_client_close_connection(tcp_client_manager_t *manager, uint32_t connection_id);
int tcp_client_send_data(tcp_connection_t *conn, const uint8_t *data, size_t len);

#endif