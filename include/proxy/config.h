#ifndef PROXY_CONFIG_H
#define PROXY_CONFIG_H

#include <stdint.h>

typedef struct {
    // SITP configuration
    char *sitp_interface;
    int sitp_mtu;
    uint16_t sitp_protocol;
    uint16_t sitp_local_board_id;
    uint16_t sitp_remote_board_id;
    uint16_t sitp_local_port;
    uint16_t sitp_remote_port;
    long sitp_send_delay_ns;
    
    // Target server configuration
    char *target_host;
    int target_port;
    
    // Connection management
    int connection_timeout;
    int connection_pool_size;
    
    // Logging configuration
    int log_level;
    int log_to_file;
    char *log_filename;
    
    // General configuration
    int padding_size;
    int verbose;
    
} proxy_config_t;

int proxy_config_load(const char *config_file, proxy_config_t *config);
void proxy_config_free(proxy_config_t *config);

#endif