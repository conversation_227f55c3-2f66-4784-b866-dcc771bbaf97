#ifndef BRIDGE_DATA_FLOW_H
#define BRIDGE_DATA_FLOW_H

#include "bridge/tcp_server.h"
#include "bridge/sitp_client.h"
#include "bridge/pipes.h"

typedef struct bridge_data_flow {
    tcp_server_t *tcp_server;
    sitp_client_t *sitp_client;
    bridge_pipes_t *pipes;
    bridge_config_t *config;
} bridge_data_flow_t;

int bridge_data_flow_init(bridge_data_flow_t *flow, tcp_server_t *tcp_server,
                         sitp_client_t *sitp_client, bridge_pipes_t *pipes,
                         bridge_config_t *config);

void tcp_to_sitp_pipe_cb(int fd, short events, void *arg);
void sitp_to_tcp_pipe_cb(int fd, short events, void *arg);

#endif