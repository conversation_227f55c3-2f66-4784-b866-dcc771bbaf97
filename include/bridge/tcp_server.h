#ifndef BRIDGE_TCP_SERVER_H
#define BRIDGE_TCP_SERVER_H

#include <event2/event.h>
#include <event2/listener.h>
#include "bridge/config.h"
#include "bridge/pipes.h"

typedef struct tcp_client {
    int fd;
    struct event *read_event;
    struct bufferevent *bev;
    struct tcp_server *server;
    struct tcp_client *next;
} tcp_client_t;

typedef struct tcp_server {
    struct evconnlistener *listener;
    struct event_base *base;
    bridge_pipes_t *pipes;
    bridge_config_t *config;
    tcp_client_t *clients;
    int running;
} tcp_server_t;

int tcp_server_init(tcp_server_t *server, struct event_base *base, 
                   bridge_config_t *config, bridge_pipes_t *pipes);
int tcp_server_start(tcp_server_t *server);
void tcp_server_stop(tcp_server_t *server);
void tcp_server_cleanup(tcp_server_t *server);
tcp_client_t *tcp_server_find_client(tcp_server_t *server, int fd);
void tcp_server_close_client(tcp_server_t *server, tcp_client_t *client);

#endif