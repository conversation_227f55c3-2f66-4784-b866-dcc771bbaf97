#ifndef BRIDGE_PIPES_H
#define BRIDGE_PIPES_H

#include <event2/event.h>

typedef struct bridge_pipes {
    int tcp_to_sitp_fd[2];    
    int sitp_to_tcp_fd[2];    
    struct event *tcp_to_sitp_event;  
    struct event *sitp_to_tcp_event;  
    struct event_base *base;
} bridge_pipes_t;

typedef void (*pipe_data_callback_t)(int fd, short events, void *arg);

int bridge_pipes_init(bridge_pipes_t *pipes, struct event_base *base);
void bridge_pipes_cleanup(bridge_pipes_t *pipes);
int bridge_pipes_set_callbacks(bridge_pipes_t *pipes, 
                               pipe_data_callback_t tcp_to_sitp_cb,
                               pipe_data_callback_t sitp_to_tcp_cb,
                               void *tcp_to_sitp_arg,
                               void *sitp_to_tcp_arg);

#endif